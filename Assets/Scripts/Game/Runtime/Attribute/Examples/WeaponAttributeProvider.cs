using UnityEngine;
using System.Collections.Generic;
using System;

namespace Game.AceFall
{
    [System.Serializable]
    public class WeaponData
    {
        public string WeaponName;
        public float AttackPower;
        public float CriticalRate;
        public float CriticalDamage;
        public float AttackSpeed;
        
        public WeaponData(string name, float attack, float critRate = 0f, float critDamage = 0f, float attackSpeed = 0f)
        {
            WeaponName = name;
            AttackPower = attack;
            CriticalRate = critRate;
            CriticalDamage = critDamage;
            AttackSpeed = attackSpeed;
        }
    }

    public class WeaponAttributeProvider : MonoBehaviour, IAttributeProvider
    {
        [SerializeField] private WeaponData m_weaponData;
        [SerializeField] private bool m_isEquipped = false;
        
        private List<AttributeModifier> m_modifiers = new();

        public string ProviderId => $"Weapon_{m_weaponData?.WeaponName ?? "Unknown"}";
        public bool IsActive => m_isEquipped && m_weaponData != null;
        
        public event Action<IAttributeProvider> OnModifiersChanged;

        public WeaponData WeaponData => m_weaponData;

        public void SetWeaponData(WeaponData weaponData)
        {
            m_weaponData = weaponData;
            RefreshModifiers();
        }

        public void Equip()
        {
            if (m_isEquipped) return;
            
            m_isEquipped = true;
            RefreshModifiers();
            Debug.Log($"装备武器: {m_weaponData?.WeaponName}");
        }

        public void Unequip()
        {
            if (!m_isEquipped) return;
            
            m_isEquipped = false;
            RefreshModifiers();
            Debug.Log($"卸下武器: {m_weaponData?.WeaponName}");
        }

        private void RefreshModifiers()
        {
            ClearModifiers();
            
            if (!IsActive || m_weaponData == null)
                return;

            if (m_weaponData.AttackPower > 0)
            {
                AddModifier(CreateModifier(AttributeType.Attack, 0f, m_weaponData.AttackPower));
            }

            if (m_weaponData.CriticalRate > 0)
            {
                AddModifier(CreateModifier(AttributeType.CriticalRate, 0f, 0f, m_weaponData.CriticalRate));
            }

            if (m_weaponData.CriticalDamage > 0)
            {
                AddModifier(CreateModifier(AttributeType.CriticalDamage, 0f, 0f, m_weaponData.CriticalDamage));
            }

            if (m_weaponData.AttackSpeed > 0)
            {
                AddModifier(CreateModifier(AttributeType.AttackSpeed, 0f, 0f, m_weaponData.AttackSpeed));
            }
        }

        public IReadOnlyList<AttributeModifier> GetAttributeModifiers()
        {
            return m_modifiers;
        }

        protected void AddModifier(AttributeModifier modifier)
        {
            m_modifiers.Add(modifier);
            NotifyModifiersChanged();
        }

        protected void RemoveModifier(AttributeModifier modifier)
        {
            m_modifiers.Remove(modifier);
            NotifyModifiersChanged();
        }

        protected void ClearModifiers()
        {
            m_modifiers.Clear();
            NotifyModifiersChanged();
        }

        protected void NotifyModifiersChanged()
        {
            OnModifiersChanged?.Invoke(this);
        }

        protected AttributeModifier CreateModifier(AttributeType type, float baseValue = 0f, float flatBonus = 0f, float percentBonus = 0f)
        {
            return new AttributeModifier(type, baseValue, flatBonus, percentBonus, ProviderId);
        }

        private void Awake()
        {
            if (m_weaponData == null)
            {
                m_weaponData = new WeaponData("默认武器", 5f, 0.1f, 0.5f, 0.2f);
            }
        }
    }
}