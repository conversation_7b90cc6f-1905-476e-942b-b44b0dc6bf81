using System;
using System.Collections.Generic;

namespace Game.AceFall
{
    [Serializable]
    public struct DynamicAttributeId : IEquatable<DynamicAttributeId>
    {
        public static readonly DynamicAttributeId Invalid = new DynamicAttributeId(0, "Invalid");
        
        private static int s_nextId = 1;
        private static readonly Dictionary<string, DynamicAttributeId> s_registeredAttributes = new();
        private static readonly Dictionary<int, string> s_idToName = new();
        
        public readonly int Id;
        public readonly string Name;
        
        private DynamicAttributeId(int id, string name)
        {
            Id = id;
            Name = name;
        }
        
        public static DynamicAttributeId Register(string name)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentException("Attribute name cannot be null or empty", nameof(name));
                
            if (s_registeredAttributes.TryGetValue(name, out var existing))
                return existing;
                
            var id = s_nextId++;
            var attributeId = new DynamicAttributeId(id, name);
            
            s_registeredAttributes[name] = attributeId;
            s_idToName[id] = name;
            
            return attributeId;
        }
        
        public static DynamicAttributeId Get(string name)
        {
            return s_registeredAttributes.TryGetValue(name, out var id) ? id : Invalid;
        }
        
        public static bool IsValid(DynamicAttributeId id)
        {
            return id.Id > 0 && s_idToName.ContainsKey(id.Id);
        }
        
        public static IEnumerable<DynamicAttributeId> GetAllRegistered()
        {
            return s_registeredAttributes.Values;
        }
        
        public bool Equals(DynamicAttributeId other)
        {
            return Id == other.Id;
        }
        
        public override bool Equals(object obj)
        {
            return obj is DynamicAttributeId other && Equals(other);
        }
        
        public override int GetHashCode()
        {
            return Id;
        }
        
        public override string ToString()
        {
            return Name;
        }
        
        public static bool operator ==(DynamicAttributeId left, DynamicAttributeId right)
        {
            return left.Equals(right);
        }
        
        public static bool operator !=(DynamicAttributeId left, DynamicAttributeId right)
        {
            return !left.Equals(right);
        }
        
        public static implicit operator string(DynamicAttributeId id)
        {
            return id.Name;
        }
    }
}