using System;

namespace Game.AceFall
{
    [Serializable]
    public struct DynamicAttributeValue : IEquatable<DynamicAttributeValue>
    {
        public float BaseValue;
        public float FlatBonus;
        public float PercentBonus;

        public float FinalValue => (BaseValue + FlatBonus) * (1f + PercentBonus);

        public DynamicAttributeValue(float baseValue, float flatBonus = 0f, float percentBonus = 0f)
        {
            BaseValue = baseValue;
            FlatBonus = flatBonus;
            PercentBonus = percentBonus;
        }

        public static DynamicAttributeValue operator +(DynamicAttributeValue a, DynamicAttributeValue b)
        {
            return new DynamicAttributeValue(
                a.BaseValue + b.BaseValue,
                a.<PERSON>Bonus + b.FlatBonus,
                a.PercentBonus + b.PercentBonus
            );
        }

        public static DynamicAttributeValue operator -(DynamicAttributeValue a, DynamicAttributeValue b)
        {
            return new DynamicAttributeValue(
                a.BaseValue - b.<PERSON>Value,
                a.FlatBonus - b.<PERSON>,
                a.PercentBonus - b.PercentBonus
            );
        }

        public static DynamicAttributeValue operator *(DynamicAttributeValue a, float multiplier)
        {
            return new DynamicAttributeValue(
                a.BaseValue * multiplier,
                a.FlatBonus * multiplier,
                a.PercentBonus * multiplier
            );
        }

        public static implicit operator float(DynamicAttributeValue value)
        {
            return value.FinalValue;
        }

        public static implicit operator DynamicAttributeValue(float value)
        {
            return new DynamicAttributeValue(value);
        }

        public bool Equals(DynamicAttributeValue other)
        {
            return Math.Abs(BaseValue - other.BaseValue) < 0.001f &&
                   Math.Abs(FlatBonus - other.FlatBonus) < 0.001f &&
                   Math.Abs(PercentBonus - other.PercentBonus) < 0.001f;
        }

        public override bool Equals(object obj)
        {
            return obj is DynamicAttributeValue other && Equals(other);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(BaseValue, FlatBonus, PercentBonus);
        }

        public override string ToString()
        {
            return $"Base: {BaseValue:F2}, Flat: {FlatBonus:F2}, Percent: {PercentBonus:P2}, Final: {FinalValue:F2}";
        }
    }

    [Serializable]
    public struct DynamicAttributeModifier : IEquatable<DynamicAttributeModifier>
    {
        public DynamicAttributeId AttributeId;
        public float BaseValue;
        public float FlatBonus;
        public float PercentBonus;
        public string Source;
        public int Priority;

        public DynamicAttributeValue ToAttributeValue()
        {
            return new DynamicAttributeValue(BaseValue, FlatBonus, PercentBonus);
        }

        public DynamicAttributeModifier(DynamicAttributeId attributeId, float baseValue = 0f, float flatBonus = 0f, 
            float percentBonus = 0f, string source = "", int priority = 0)
        {
            AttributeId = attributeId;
            BaseValue = baseValue;
            FlatBonus = flatBonus;
            PercentBonus = percentBonus;
            Source = source;
            Priority = priority;
        }

        public bool Equals(DynamicAttributeModifier other)
        {
            return AttributeId == other.AttributeId &&
                   Math.Abs(BaseValue - other.BaseValue) < 0.001f &&
                   Math.Abs(FlatBonus - other.FlatBonus) < 0.001f &&
                   Math.Abs(PercentBonus - other.PercentBonus) < 0.001f &&
                   Source == other.Source &&
                   Priority == other.Priority;
        }

        public override bool Equals(object obj)
        {
            return obj is DynamicAttributeModifier other && Equals(other);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(AttributeId, BaseValue, FlatBonus, PercentBonus, Source, Priority);
        }
    }
}