using UnityEngine;
using XFrame;

namespace Game.AceFall
{
    public class DynamicAttributeExample : MonoBehaviour
    {
        private DynamicAttributeManager m_attributeManager;
        
        private void Start()
        {
            Logger.Info("DynamicAttributeExample 开始演示动态属性系统");
            
            m_attributeManager = GetComponent<DynamicAttributeManager>();
            if (m_attributeManager == null)
            {
                m_attributeManager = gameObject.AddComponent<DynamicAttributeManager>();
            }
            
            DemoCustomAttributes();
            DemoRuntimeAttributeCreation();
        }
        
        private void DemoCustomAttributes()
        {
            Logger.Info("=== 演示自定义属性 ===");
            
            var luck = m_attributeManager.RegisterAttribute("Luck", "幸运值", "特殊");
            var experience = m_attributeManager.RegisterAttribute("Experience", "经验值", "角色");
            var level = m_attributeManager.RegisterAttribute("Level", "等级", "角色");
            
            m_attributeManager.SetBaseAttribute(luck, 50f);
            m_attributeManager.SetBaseAttribute(experience, 0f);
            m_attributeManager.SetBaseAttribute(level, 1f);
            
            Logger.Info($"幸运值: {m_attributeManager.GetAttributeValue(luck)}");
            Logger.Info($"经验值: {m_attributeManager.GetAttributeValue(experience)}");
            Logger.Info($"等级: {m_attributeManager.GetAttributeValue(level)}");
        }
        
        private void DemoRuntimeAttributeCreation()
        {
            Logger.Info("=== 演示运行时属性创建 ===");
            
            for (int i = 1; i <= 3; i++)
            {
                var skillId = m_attributeManager.RegisterAttribute($"Skill{i}Level", $"技能{i}等级", "技能");
                m_attributeManager.SetBaseAttribute(skillId, i * 10f);
                Logger.Info($"技能{i}等级: {m_attributeManager.GetAttributeValue(skillId)}");
            }
            
            var resistanceTypes = new[] { "Fire", "Ice", "Lightning", "Poison" };
            foreach (var type in resistanceTypes)
            {
                var resistanceId = m_attributeManager.RegisterAttribute($"{type}Resistance", $"{type}抗性", "抗性");
                m_attributeManager.SetBaseAttribute(resistanceId, Random.Range(0f, 50f));
                Logger.Info($"{type}抗性: {m_attributeManager.GetAttributeValue(resistanceId):F1}");
            }
        }
        
        [ContextMenu("Show All Attributes")]
        public void ShowAllAttributes()
        {
            Logger.Info("=== 所有注册的属性 ===");
            
            foreach (var attributeId in m_attributeManager.GetAllAttributes())
            {
                var info = DynamicAttributeInfo.GetInfo(attributeId);
                var value = m_attributeManager.GetAttributeValue(attributeId);
                Logger.Info($"{info.DisplayName} ({info.Category}): {value:F2}");
            }
        }
        
        [ContextMenu("Add Random Buff")]
        public void AddRandomBuff()
        {
            var buffProvider = gameObject.AddComponent<DynamicWeaponAttributeProvider>();
            
            var attributeNames = new[] { "Attack", "Defense", "Speed", "Luck" };
            var randomAttribute = attributeNames[Random.Range(0, attributeNames.Length)];
            var randomBonus = Random.Range(5f, 20f);
            
            buffProvider.AddModifier(randomAttribute, randomBonus);
            m_attributeManager.RegisterProvider(buffProvider);
            
            Logger.Info($"添加随机Buff: {randomAttribute} +{randomBonus:F1}");
        }
    }
}