using System;
using System.Collections.Generic;

namespace Game.AceFall
{
    [Serializable]
    public class DynamicAttributeInfo
    {
        private static readonly Dictionary<DynamicAttributeId, DynamicAttributeInfo> s_attributeInfos = new();
        
        public DynamicAttributeId Id { get; }
        public string DisplayName { get; set; }
        public bool HasCurrentValue { get; set; }
        public DynamicAttributeId MaxValueAttribute { get; set; }
        public float MinValue { get; set; } = 0f;
        public float MaxValue { get; set; } = float.MaxValue;
        public string Category { get; set; } = "Default";
        public string Description { get; set; } = "";
        public bool IsPercentage { get; set; } = false;
        
        public DynamicAttributeInfo(DynamicAttributeId id, string displayName = null)
        {
            Id = id;
            DisplayName = displayName ?? id.Name;
        }
        
        public static void RegisterInfo(DynamicAttributeInfo info)
        {
            if (info == null || info.Id == DynamicAttributeId.Invalid)
                return;
                
            s_attributeInfos[info.Id] = info;
        }
        
        public static DynamicAttributeInfo GetInfo(DynamicAttributeId id)
        {
            return s_attributeInfos.TryGetValue(id, out var info) ? info : CreateDefaultInfo(id);
        }
        
        private static DynamicAttributeInfo CreateDefaultInfo(DynamicAttributeId id)
        {
            if (id == DynamicAttributeId.Invalid)
                return null;
                
            var info = new DynamicAttributeInfo(id);
            s_attributeInfos[id] = info;
            return info;
        }
        
        public static IEnumerable<DynamicAttributeInfo> GetAllInfos()
        {
            return s_attributeInfos.Values;
        }
        
        public DynamicAttributeInfo SetDisplayName(string displayName)
        {
            DisplayName = displayName;
            return this;
        }
        
        public DynamicAttributeInfo SetHasCurrentValue(bool hasCurrentValue, DynamicAttributeId maxValueAttribute = default)
        {
            HasCurrentValue = hasCurrentValue;
            if (hasCurrentValue && maxValueAttribute != DynamicAttributeId.Invalid)
            {
                MaxValueAttribute = maxValueAttribute;
            }
            return this;
        }
        
        public DynamicAttributeInfo SetRange(float minValue, float maxValue)
        {
            MinValue = minValue;
            MaxValue = maxValue;
            return this;
        }
        
        public DynamicAttributeInfo SetCategory(string category)
        {
            Category = category;
            return this;
        }
        
        public DynamicAttributeInfo SetDescription(string description)
        {
            Description = description;
            return this;
        }
        
        public DynamicAttributeInfo SetIsPercentage(bool isPercentage)
        {
            IsPercentage = isPercentage;
            return this;
        }
    }
}