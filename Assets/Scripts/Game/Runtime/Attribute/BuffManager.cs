using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Game.AceFall
{
    public class BuffManager : MonoBehaviour
    {
        [SerializeField] private AttributeAttachmentSystem m_attachmentSystem;
        [SerializeField] private int m_maxBuffsPerProvider = 20;
        [SerializeField] private int m_maxBuffProviders = 10;
        
        private readonly List<BuffAttributeProvider> m_buffProviders = new();
        private readonly Dictionary<string, BuffInstance> m_allBuffs = new();
        
        public event Action<BuffInstance> OnBuffAdded;
        public event Action<BuffInstance> OnBuffRemoved;
        public event Action<BuffInstance> OnBuffStackChanged;

        private void Awake()
        {
            if (m_attachmentSystem == null)
                m_attachmentSystem = GetComponent<AttributeAttachmentSystem>();
        }

        private void Start()
        {
            InitializeBuffSlots();
        }

        private void InitializeBuffSlots()
        {
            // 自由Buff系统：需要时动态创建BuffAttributeProvider
            // 不再需要预先创建固定槽位
        }

        private void SetupBuffProviderEvents(BuffAttributeProvider provider)
        {
            provider.OnBuffAdded += OnBuffAddedInternal;
            provider.OnBuffRemoved += OnBuffRemovedInternal;
            provider.OnBuffStackChanged += OnBuffStackChangedInternal;
        }

        private void OnBuffAddedInternal(BuffInstance buffInstance)
        {
            m_allBuffs[buffInstance.BuffData.BuffId] = buffInstance;
            OnBuffAdded?.Invoke(buffInstance);
        }

        private void OnBuffRemovedInternal(BuffInstance buffInstance)
        {
            m_allBuffs.Remove(buffInstance.BuffData.BuffId);
            OnBuffRemoved?.Invoke(buffInstance);
        }

        private void OnBuffStackChangedInternal(BuffInstance buffInstance)
        {
            OnBuffStackChanged?.Invoke(buffInstance);
        }

        /// <summary>
        /// 添加Buff到自由槽位
        /// </summary>
        public bool AddBuff(BuffData buffData, string sourceId = "")
        {
            // 首先检查是否已存在相同的Buff
            var existingBuff = GetBuff(buffData.BuffId);
            if (existingBuff != null)
            {
                // 找到包含此Buff的提供者并尝试添加
                foreach (var provider in m_buffProviders)
                {
                    if (provider.HasBuff(buffData.BuffId))
                    {
                        return provider.AddBuff(buffData, sourceId);
                    }
                }
            }

            // 选择最佳的BuffAttributeProvider
            var targetProvider = SelectBestBuffProvider(buffData);
            if (targetProvider != null)
            {
                return targetProvider.AddBuff(buffData, sourceId);
            }

            // 如果没有合适的提供者，创建新的
            if (m_buffProviders.Count < m_maxBuffProviders)
            {
                var newProvider = CreateNewBuffProvider($"BuffProvider_{m_buffProviders.Count + 1}");
                if (newProvider != null)
                {
                    return newProvider.AddBuff(buffData, sourceId);
                }
            }

            Debug.LogWarning($"无法为Buff {buffData.BuffName} 找到或创建合适的槽位");
            return false;
        }

        private BuffAttributeProvider SelectBestBuffProvider(BuffData buffData)
        {
            // 优先级策略：
            // 1. 如果是可叠加Buff，寻找已有相同ID的提供者
            // 2. 选择最空闲的提供者
            // 3. 优先级越高的Buff，越优先使用负载较低的提供者

            BuffAttributeProvider bestProvider = null;
            int minBuffCount = int.MaxValue;

            foreach (var provider in m_buffProviders)
            {
                var buffCount = provider.GetAllBuffs().Count;
                
                // 如果这个提供者有空间且Buff数量更少
                if (buffCount < m_maxBuffsPerProvider && buffCount < minBuffCount)
                {
                    bestProvider = provider;
                    minBuffCount = buffCount;
                }
            }

            return bestProvider;
        }

        private BuffAttributeProvider CreateNewBuffProvider(string providerName)
        {
            var newProvider = m_attachmentSystem.AttachTo<BuffAttributeProvider>(AttachmentSlotType.BuffContainer, providerName);
            if (newProvider != null)
            {
                m_buffProviders.Add(newProvider);
                SetupBuffProviderEvents(newProvider);
                Debug.Log($"创建新的BuffAttributeProvider: {providerName}");
                return newProvider;
            }
            return null;
        }

        /// <summary>
        /// 移除指定ID的Buff
        /// </summary>
        public bool RemoveBuff(string buffId)
        {
            foreach (var provider in m_buffProviders)
            {
                if (provider.RemoveBuff(buffId))
                {
                    CleanupEmptyProviders();
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 根据名称移除Buff
        /// </summary>
        public bool RemoveBuffByName(string buffName)
        {
            foreach (var provider in m_buffProviders)
            {
                if (provider.RemoveBuffByName(buffName))
                {
                    CleanupEmptyProviders();
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 移除指定来源的所有Buff
        /// </summary>
        public void RemoveBuffsBySource(string sourceId)
        {
            foreach (var provider in m_buffProviders)
            {
                provider.RemoveBuffsBySource(sourceId);
            }
            CleanupEmptyProviders();
        }

        /// <summary>
        /// 移除所有Buff
        /// </summary>
        public void RemoveAllBuffs()
        {
            foreach (var provider in m_buffProviders)
            {
                provider.RemoveAllBuffs();
            }
            CleanupEmptyProviders();
        }

        /// <summary>
        /// 清理空的BuffAttributeProvider
        /// </summary>
        private void CleanupEmptyProviders()
        {
            for (int i = m_buffProviders.Count - 1; i >= 0; i--)
            {
                var provider = m_buffProviders[i];
                if (provider.GetAllBuffs().Count == 0)
                {
                    m_buffProviders.RemoveAt(i);
                    // 从AttachmentSystem中移除
                    m_attachmentSystem.DetachFrom(provider);
                }
            }
        }

        /// <summary>
        /// 获取指定ID的Buff
        /// </summary>
        public BuffInstance GetBuff(string buffId)
        {
            return m_allBuffs.TryGetValue(buffId, out var buff) ? buff : null;
        }

        /// <summary>
        /// 根据名称获取Buff
        /// </summary>
        public BuffInstance GetBuffByName(string buffName)
        {
            return m_allBuffs.Values.FirstOrDefault(b => b.BuffData.BuffName == buffName);
        }

        /// <summary>
        /// 获取所有Buff
        /// </summary>
        public IReadOnlyList<BuffInstance> GetAllBuffs()
        {
            return m_allBuffs.Values.ToList();
        }

        /// <summary>
        /// 检查是否有指定Buff
        /// </summary>
        public bool HasBuff(string buffId)
        {
            return m_allBuffs.ContainsKey(buffId);
        }

        /// <summary>
        /// 根据名称检查是否有Buff
        /// </summary>
        public bool HasBuffByName(string buffName)
        {
            return m_allBuffs.Values.Any(b => b.BuffData.BuffName == buffName);
        }

        /// <summary>
        /// 获取Buff叠加层数
        /// </summary>
        public int GetBuffStacks(string buffId)
        {
            var buff = GetBuff(buffId);
            return buff?.CurrentStacks ?? 0;
        }

        /// <summary>
        /// 获取指定类型的所有Buff
        /// </summary>
        public IReadOnlyList<BuffInstance> GetBuffsByStackType(BuffStackType stackType)
        {
            return m_allBuffs.Values.Where(b => b.BuffData.StackType == stackType).ToList();
        }

        /// <summary>
        /// 获取指定优先级的所有Buff
        /// </summary>
        public IReadOnlyList<BuffInstance> GetBuffsByPriority(BuffPriority priority)
        {
            return m_allBuffs.Values.Where(b => b.BuffData.Priority == priority).ToList();
        }

        /// <summary>
        /// 获取即将到期的Buff（剩余时间少于指定秒数）
        /// </summary>
        public IReadOnlyList<BuffInstance> GetExpiringBuffs(float timeThreshold)
        {
            return m_allBuffs.Values
                .Where(b => !b.BuffData.IsPermanent && b.RemainingTime <= timeThreshold)
                .ToList();
        }

        /// <summary>
        /// 暂停所有Buff的时间更新
        /// </summary>
        public void PauseAllBuffs()
        {
            foreach (var provider in m_buffProviders)
            {
                provider.enabled = false;
            }
        }

        /// <summary>
        /// 恢复所有Buff的时间更新
        /// </summary>
        public void ResumeAllBuffs()
        {
            foreach (var provider in m_buffProviders)
            {
                provider.enabled = true;
            }
        }

        /// <summary>
        /// 获取Buff统计信息
        /// </summary>
        public void GetBuffStatistics(out int totalBuffs, out int stackableBuffs, out int permanentBuffs)
        {
            totalBuffs = m_allBuffs.Count;
            stackableBuffs = m_allBuffs.Values.Count(b => b.BuffData.StackType == BuffStackType.Stack);
            permanentBuffs = m_allBuffs.Values.Count(b => b.BuffData.IsPermanent);
        }

        private void OnDestroy()
        {
            // 清理事件监听
            foreach (var provider in m_buffProviders)
            {
                if (provider != null)
                {
                    provider.OnBuffAdded -= OnBuffAddedInternal;
                    provider.OnBuffRemoved -= OnBuffRemovedInternal;
                    provider.OnBuffStackChanged -= OnBuffStackChangedInternal;
                }
            }
        }
    }
}