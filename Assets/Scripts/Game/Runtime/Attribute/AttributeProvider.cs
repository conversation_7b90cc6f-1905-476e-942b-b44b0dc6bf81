using System.Collections.Generic;
using XFrame;

namespace Game.AceFall
{
    public abstract class AttributeProvider
    {
        public abstract string ProviderId { get; }
        public virtual bool IsActive { get; protected set; } = true;
        public HashSet<AttributeModifier> Modifiers = new();

        private XCallback<AttributeProvider> _onModifiersChanged;
        private Dictionary<EAttributeType, float> _attributes = new();
        private Dictionary<EAttributeType, float> _cachedAttributes = new();

        public void SetActive(bool active)
        {
            if (IsActive != active)
            {
                IsActive = active;
                _onModifiersChanged?.Invoke(this);
            }
        }

        public void AddModifier(AttributeModifier modifier)
        {
            if (Modifiers.Add(modifier))
            {
                _onModifiersChanged?.Invoke(this);
            }
        }

        public void RemoveModifier(AttributeModifier modifier)
        {
            if (Modifiers.Remove(modifier))
            {
                _onModifiersChanged?.Invoke(this);
            }
        }

        public void ClearModifiers()
        {
            Modifiers.Clear();

            _onModifiersChanged?.Invoke(this);
        }

        public void AddListener(XCallback<AttributeProvider> callback)
        {
            _onModifiersChanged += callback;
        }

        public void RemoveListener(XCallback<AttributeProvider> callback)
        {
            _onModifiersChanged -= callback;
        }
    }
}