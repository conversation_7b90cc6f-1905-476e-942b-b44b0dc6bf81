using UnityEngine;

namespace XFrame
{
    public static class ValueExtension
    {
        public static int ToFloorInt(this float value)
        {
            return Mathf.FloorToInt(value);
        }

        public static int ToCeilInt(this float value)
        {
            return Mathf.CeilToInt(value);
        }

        public static int ToInt(this float value)
        {
            return (int)value;
        }
    }
}