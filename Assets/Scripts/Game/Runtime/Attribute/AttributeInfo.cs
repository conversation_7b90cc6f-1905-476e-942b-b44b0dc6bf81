using System.Collections.Generic;

namespace Game.AceFall
{
    public struct AttributeInfo
    {
        public static readonly AttributeInfo Empty = new AttributeInfo(EAttributeType.None);

        public readonly EAttributeType Type;
        public readonly EAttributeType MaxType;

        public AttributeInfo(EAttributeType type, EAttributeType maxType = EAttributeType.None)
        {
            Type = type;
            MaxType = maxType;
        }
    }

    public static class AttributeInfoExtensions
    {
        private static readonly Dictionary<EAttributeType, AttributeInfo> s_AttributeInfos = new()
        {
            { EAttributeType.Health, new AttributeInfo(EAttributeType.Health) },
            { EAttributeType.MaxHealth, new AttributeInfo(EAttributeType.MaxHealth) },
            { EAttributeType.Mana, new AttributeInfo(EAttributeType.Mana) },
            { EAttributeType.MaxMana, new AttributeInfo(EAttributeType.MaxMana) },
            { EAttributeType.Strength, new AttributeInfo(EAttributeType.Strength) },
            { EAttributeType.Attack, new AttributeInfo(EAttributeType.Attack) },
        };
        
        public static AttributeInfo GetInfo(this EAttributeType type)
        {
            return s_AttributeInfos.GetValueOrDefault(type, AttributeInfo.Empty);
        }

        public static EAttributeType GetMaxValueType(this EAttributeType type)
        {
            return GetInfo(type).MaxType;
        }
        
        public static bool HasCurrentValue(this EAttributeType type)
        {
            return GetInfo(type).MaxType != EAttributeType.None;
        }
    }
}