using System;
using UnityEngine;
using XFrame;
using Logger = XFrame.Logger;

namespace Game.AceFall
{
	public class GameEntry : MonoBehaviourPro
	{
		private static GameEntry _Singleton;
		public static GameEventModule Event { get; private set; }
		public static DataTableModule DataTable { get; private set; }
		public static SceneModule Scene { get; private set; }

		public static cfg.Tables Tables => DataTable.Tables;

		private void Start()
		{
			if (_Singleton != null)
			{
				Logger.Error("重复初始化GameEntry!!!");
				return;
			}
			_Singleton = this;
			DontDestroyOnLoad(gameObject);

			Initialize();
		}

		void Initialize()
		{
			UnityEngine.Debug.Log("初始化");
			Logger.Info("GameEntry 开始初始化");
			CoreEntry.Singleton.Initialize();

			Event = CoreEntry.ExternalRegistModule<GameEventModule>(transform, "GameEventModule");
			DataTable = CoreEntry.ExternalRegistModule<DataTableModule>(transform, "DataTableModule");
			Scene = CoreEntry.ExternalRegistModule<SceneModule>(transform, "SceneModule");

			Event.AddListener(EGameEvent.EnterGaming, OnEnterGaming);

			CoreEntry.Procedure.AddProcedure(new ProcedureInit());
			CoreEntry.Procedure.AddProcedure(new ProcedureResInitError());
			CoreEntry.Procedure.AddProcedure(new ProcedureLoadDataTable());
			CoreEntry.Procedure.AddProcedure(new ProcedureFinish());

			CoreEntry.Procedure.EnterProcedure<ProcedureInit>();
		}

		private void OnEnterGaming()
		{
			UnityEngine.Debug.Log("游戏流程开始");
			Info("游戏流程开始");

			// CoreEntry.Res.LoadGameObject(null,"GameObject");
			Scene.LoadSceneAsync("Game").Forget();
		}

	}
}