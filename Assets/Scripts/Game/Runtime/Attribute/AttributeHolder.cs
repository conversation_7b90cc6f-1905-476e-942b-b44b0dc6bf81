using System.Collections.Generic;
using UnityEngine;
using XFrame;

namespace Game.AceFall
{
    public class AttributeHolder : MonoBehaviour
    {
        private EAttributeTarget TargetType;
        private Dictionary<EAttributeType, float> _attributes = new();
        private Dictionary<EAttributeType, float> _cachedAttributes = new();

        private HashSet<AttributeProvider> _providers = new();
        private XCallback<EAttributeType, float, float> _onAttributeChanged;

        private HashSet<AttributeModifier> Modifiers = new();

        public void SetTargetType(EAttributeTarget targetType)
        {
            TargetType = targetType;
            RecalculateAttribute();
        }

        public void SetAttribute(EAttributeType type, float value)
        {
            var maxType = type.GetMaxValueType();
            if (maxType != EAttributeType.None)
            {
                value = Mathf.Clamp(value, 0f, GetAttributeValue(maxType));
            }

            _attributes[type] = value;
        }

        private float GetAttributeValue(EAttributeType maxType)
        {
            var value = _cachedAttributes.GetValueOrDefault(maxType, 0f);
            return value;
        }

        public void Attach(AttributeProvider provider)
        {
            if (_providers.Add(provider))
            {
                provider.AddListener(OnProviderModifiersChanged);
                RecalculateAttribute();
            }
        }

        public void Detach(AttributeProvider provider)
        {
            if (_providers.Remove(provider))
            {
                provider.RemoveListener(OnProviderModifiersChanged);
                RecalculateAttribute();
            }
        }

        public void AddModifier(AttributeModifier modifier)
        {
            RecalculateAttribute();
        }

        public void RemoveModifier(AttributeModifier modifier)
        {
            RecalculateAttribute();
        }

        private void OnProviderModifiersChanged(AttributeProvider provider)
        {
            RecalculateAttribute();
        }

        private void RecalculateAttribute()
        {
        }

        public void AddListener(XCallback<EAttributeType, float, float> callback)
        {
            _onAttributeChanged += callback;
        }

        public void RemoveListener(XCallback<EAttributeType, float, float> callback)
        {
            _onAttributeChanged -= callback;
        }
    }
}