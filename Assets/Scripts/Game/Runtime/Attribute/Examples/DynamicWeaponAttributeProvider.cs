using System;
using System.Collections.Generic;
using UnityEngine;

namespace Game.AceFall
{
    public class DynamicWeaponAttributeProvider : MonoBehaviour, IDynamicAttributeProvider
    {
        [SerializeField] private string m_weaponId = "TestWeapon";
        [SerializeField] private bool m_isEquipped = true;
        [SerializeField] private List<DynamicAttributeModifierData> m_modifiers = new();

        public string ProviderId => $"Weapon_{m_weaponId}";
        public bool IsActive => m_isEquipped && gameObject.activeInHierarchy;

        public event Action<IDynamicAttributeProvider> OnModifiersChanged;

        private void Start()
        {
            InitializeDefaultModifiers();
        }

        private void InitializeDefaultModifiers()
        {
            if (m_modifiers.Count == 0)
            {
                var attack = DynamicAttributeId.Register("Attack");
                var attackSpeed = DynamicAttributeId.Register("AttackSpeed");
                var criticalRate = DynamicAttributeId.Register("CriticalRate");

                m_modifiers.Add(new DynamicAttributeModifierData
                {
                    AttributeName = "Attack",
                    FlatBonus = 15f,
                    Source = ProviderId
                });

                m_modifiers.Add(new DynamicAttributeModifierData
                {
                    AttributeName = "AttackSpeed",
                    PercentBonus = 0.2f,
                    Source = ProviderId
                });

                m_modifiers.Add(new DynamicAttributeModifierData
                {
                    AttributeName = "CriticalRate",
                    FlatBonus = 0.1f,
                    Source = ProviderId
                });

                DynamicAttributeInfo.RegisterInfo(new DynamicAttributeInfo(criticalRate, "暴击率")
                    .SetCategory("战斗")
                    .SetIsPercentage(true)
                    .SetRange(0f, 1f));
            }
        }

        public void SetEquipped(bool equipped)
        {
            if (m_isEquipped != equipped)
            {
                m_isEquipped = equipped;
                OnModifiersChanged?.Invoke(this);
            }
        }

        public void AddModifier(string attributeName, float flatBonus = 0f, float percentBonus = 0f)
        {
            var attributeId = DynamicAttributeId.Register(attributeName);
            m_modifiers.Add(new DynamicAttributeModifierData
            {
                AttributeName = attributeName,
                FlatBonus = flatBonus,
                PercentBonus = percentBonus,
                Source = ProviderId
            });
            OnModifiersChanged?.Invoke(this);
        }

        public void RemoveModifier(string attributeName)
        {
            for (int i = m_modifiers.Count - 1; i >= 0; i--)
            {
                if (m_modifiers[i].AttributeName == attributeName)
                {
                    m_modifiers.RemoveAt(i);
                    OnModifiersChanged?.Invoke(this);
                    break;
                }
            }
        }

        public IEnumerable<DynamicAttributeModifier> GetAttributeModifiers()
        {
            if (!IsActive)
                yield break;

            foreach (var data in m_modifiers)
            {
                var attributeId = DynamicAttributeId.Get(data.AttributeName);
                if (attributeId != DynamicAttributeId.Invalid)
                {
                    yield return new DynamicAttributeModifier(
                        attributeId,
                        data.BaseValue,
                        data.FlatBonus,
                        data.PercentBonus,
                        data.Source,
                        data.Priority
                    );
                }
            }
        }

        [Serializable]
        public class DynamicAttributeModifierData
        {
            public string AttributeName;
            public float BaseValue;
            public float FlatBonus;
            public float PercentBonus;
            public string Source;
            public int Priority;
        }
    }
}