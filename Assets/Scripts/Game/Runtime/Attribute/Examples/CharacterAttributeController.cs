using UnityEngine;
using System.Collections.Generic;

namespace Game.AceFall
{
    public class CharacterAttributeController : MonoBeh<PERSON><PERSON>
    {
        [Header("组件引用")]
        [SerializeField] private AttributeAttachmentSystem m_attachmentSystem;
        [SerializeField] private BuffManager m_buffManager;
        
        [Header("角色基础属性")]
        [SerializeField] private float m_baseHealth = 100f;
        [SerializeField] private float m_baseMana = 50f;
        [SerializeField] private float m_baseAttack = 10f;
        [SerializeField] private float m_baseDefense = 5f;
        
        [Header("角色基础属性点")]
        [SerializeField] private float m_strength = 10f;
        [SerializeField] private float m_agility = 10f;
        [SerializeField] private float m_intelligence = 10f;
        [SerializeField] private float m_vitality = 10f;

        private GlobalAttributeModifier m_globalModifier;
        private AttributeComboCalculator m_comboCalculator;
        private AttributeManager m_attributeManager;

        private void Awake()
        {
            if (m_attachmentSystem == null)
                m_attachmentSystem = GetComponent<AttributeAttachmentSystem>();
                
            if (m_attachmentSystem == null)
                m_attachmentSystem = gameObject.AddComponent<AttributeAttachmentSystem>();

            if (m_buffManager == null)
                m_buffManager = GetComponent<BuffManager>();
                
            if (m_buffManager == null)
                m_buffManager = gameObject.AddComponent<BuffManager>();

            m_attributeManager = m_attachmentSystem.GetAttributeManager();
        }

        private void Start()
        {
            InitializeAttributes();
            SetupGlobalModifiers();
            RegisterEventListeners();
            SetupAttachmentEvents();
        }

        private void OnDestroy()
        {
            UnregisterEventListeners();
            // 附件系统会自动清理所有附件
        }

        private void SetupAttachmentEvents()
        {
            m_attachmentSystem.OnProviderAttached += OnProviderAttached;
            m_attachmentSystem.OnProviderDetached += OnProviderDetached;
        }

        private void OnProviderAttached(IAttributeProvider provider, AttachmentSlotType slotType)
        {
            Debug.Log($"附加了 {provider.ProviderId} 到槽位 {slotType}");
        }

        private void OnProviderDetached(IAttributeProvider provider, AttachmentSlotType slotType)
        {
            Debug.Log($"分离了 {provider.ProviderId} 从槽位 {slotType}");
        }

        private void InitializeAttributes()
        {
            m_attributeManager.SetBaseAttribute(AttributeType.Strength, m_strength);
            m_attributeManager.SetBaseAttribute(AttributeType.Agility, m_agility);
            m_attributeManager.SetBaseAttribute(AttributeType.Intelligence, m_intelligence);
            m_attributeManager.SetBaseAttribute(AttributeType.Vitality, m_vitality);
            
            m_attributeManager.SetBaseAttribute(AttributeType.MaxHealth, m_baseHealth);
            m_attributeManager.SetBaseAttribute(AttributeType.MaxMana, m_baseMana);
            m_attributeManager.SetBaseAttribute(AttributeType.Attack, m_baseAttack);
            m_attributeManager.SetBaseAttribute(AttributeType.Defense, m_baseDefense);
            
            m_attributeManager.SetCurrentValue(AttributeType.Health, m_attributeManager.GetAttributeValue(AttributeType.MaxHealth));
            m_attributeManager.SetCurrentValue(AttributeType.Mana, m_attributeManager.GetAttributeValue(AttributeType.MaxMana));
        }

        private void SetupGlobalModifiers()
        {
            m_globalModifier = new GlobalAttributeModifier();
            m_globalModifier.Initialize(m_attributeManager);
            m_attributeManager.RegisterProvider(m_globalModifier);
            
            m_comboCalculator = new AttributeComboCalculator();
            m_comboCalculator.Initialize(m_attributeManager);
            m_attributeManager.RegisterProvider(m_comboCalculator);
            
            m_attributeManager.OnAttributeChanged += OnAttributeChanged;
        }

        private void OnAttributeChanged(AttributeType type, float oldValue, float newValue)
        {
            m_globalModifier.OnSourceAttributeChanged(type);
            m_comboCalculator.OnSourceAttributeChanged(type);
        }

        private void RegisterEventListeners()
        {
            GameEntry.Event.AddListener<AttributeChangedEventArg>(EGameEvent.AttributeChanged, OnAttributeChangedEvent);
            GameEntry.Event.AddListener<CurrentValueChangedEventArgs>(EGameEvent.CurrentValueChanged, OnCurrentValueChangedEvent);
        }

        private void UnregisterEventListeners()
        {
            GameEntry.Event.RemoveListener<AttributeChangedEventArg>(EGameEvent.AttributeChanged, OnAttributeChangedEvent);
            GameEntry.Event.RemoveListener<CurrentValueChangedEventArgs>(EGameEvent.CurrentValueChanged, OnCurrentValueChangedEvent);
        }

        private void OnAttributeChangedEvent(AttributeChangedEventArg args)
        {
            Debug.Log($"属性变化: {args.Type.GetDisplayName()} {args.OldValue:F1} -> {args.NewValue:F1} (来源: {args.Source})");
        }

        private void OnCurrentValueChangedEvent(CurrentValueChangedEventArgs args)
        {
            Debug.Log($"当前值变化: {args.Type.GetDisplayName()} {args.OldCurrentValue:F1} -> {args.NewCurrentValue:F1}/{args.MaxValue:F1} ({args.Percentage:P1}) (原因: {args.Reason})");
            
            if (args.Type == AttributeType.Health && args.NewCurrentValue <= 0)
            {
                Debug.Log("角色死亡!");
            }
        }

        public void TakeDamage(float damage)
        {
            var defense = m_attributeManager.GetAttributeValue(AttributeType.Defense);
            var finalDamage = Mathf.Max(1f, damage - defense);
            
            m_attributeManager.ModifyCurrentValue(AttributeType.Health, -finalDamage, "TakeDamage");
        }

        public void Heal(float amount)
        {
            m_attributeManager.ModifyCurrentValue(AttributeType.Health, amount, "Heal");
        }

        public void AddAttributePoint(AttributeType type, float amount)
        {
            if (type == AttributeType.Strength || type == AttributeType.Agility || 
                type == AttributeType.Intelligence || type == AttributeType.Vitality)
            {
                var currentValue = m_attributeManager.GetAttributeValue(type);
                m_attributeManager.SetBaseAttribute(type, currentValue + amount);
            }
        }

        /// <summary>
        /// 通用的附件附加方法
        /// </summary>
        public T AttachTo<T>(AttachmentSlotType slotType, string instanceName = null) where T : MonoBehaviour, IAttributeProvider
        {
            return m_attachmentSystem.AttachTo<T>(slotType, instanceName);
        }

        /// <summary>
        /// 附加现有的属性提供者
        /// </summary>
        public bool AttachTo(IAttributeProvider provider, AttachmentSlotType slotType)
        {
            return m_attachmentSystem.AttachTo(provider, slotType);
        }

        /// <summary>
        /// 分离指定槽位的所有附件
        /// </summary>
        public void DetachFromSlot(AttachmentSlotType slotType)
        {
            m_attachmentSystem.DetachFromSlot(slotType);
        }

        /// <summary>
        /// 通过ID分离附件
        /// </summary>
        public bool DetachById(string providerId)
        {
            return m_attachmentSystem.DetachById(providerId);
        }

        /// <summary>
        /// 获取指定槽位的附件
        /// </summary>
        public IReadOnlyList<IAttributeProvider> GetAttachmentsInSlot(AttachmentSlotType slotType)
        {
            return m_attachmentSystem.GetAttachmentsInSlot(slotType);
        }

        /// <summary>
        /// 检查槽位是否为空
        /// </summary>
        public bool IsSlotEmpty(AttachmentSlotType slotType)
        {
            return m_attachmentSystem.IsSlotEmpty(slotType);
        }

        [ContextMenu("测试伤害")]
        public void TestDamage()
        {
            TakeDamage(25f);
        }

        [ContextMenu("测试治疗")]
        public void TestHeal()
        {
            Heal(30f);
        }

        [ContextMenu("增加力量")]
        public void TestAddStrength()
        {
            AddAttributePoint(AttributeType.Strength, 5f);
        }

        [ContextMenu("装备铁剑")]
        public void TestEquipIronSword()
        {
            var weapon = AttachTo<WeaponAttributeProvider>(AttachmentSlotType.MainHand, "铁剑");
            if (weapon != null)
            {
                var ironSword = new WeaponData("铁剑", 15f, 0.1f, 0.5f, 0.2f);
                weapon.SetWeaponData(ironSword);
                weapon.Equip();
            }
        }

        [ContextMenu("装备魔法杖")]
        public void TestEquipMagicStaff()
        {
            var weapon = AttachTo<WeaponAttributeProvider>(AttachmentSlotType.MainHand, "魔法杖");
            if (weapon != null)
            {
                var magicStaff = new WeaponData("魔法杖", 8f, 0.05f, 1.0f, 0.3f);
                weapon.SetWeaponData(magicStaff);
                weapon.Equip();
            }
        }

        [ContextMenu("装备传说武器")]
        public void TestEquipLegendaryWeapon()
        {
            var weapon = AttachTo<WeaponAttributeProvider>(AttachmentSlotType.MainHand, "传说之刃");
            if (weapon != null)
            {
                var legendary = new WeaponData("传说之刃", 25f, 0.25f, 2.0f, 0.5f);
                weapon.SetWeaponData(legendary);
                weapon.Equip();
            }
        }

        [ContextMenu("卸下主手武器")]
        public void TestUnequipMainHand()
        {
            DetachFromSlot(AttachmentSlotType.MainHand);
        }

        [ContextMenu("显示主手武器")]
        public void ShowMainHandWeapon()
        {
            var attachments = GetAttachmentsInSlot(AttachmentSlotType.MainHand);
            if (attachments.Count == 0)
            {
                Debug.Log("主手没有装备武器");
                return;
            }

            foreach (var attachment in attachments)
            {
                if (attachment is WeaponAttributeProvider weapon)
                {
                    var weaponData = weapon.WeaponData;
                    Debug.Log($"=== 主手武器: {weaponData.WeaponName} ===");
                    Debug.Log($"攻击力: +{weaponData.AttackPower}");
                    Debug.Log($"暴击率: +{weaponData.CriticalRate:P1}");
                    Debug.Log($"暴击伤害: +{weaponData.CriticalDamage:P1}");
                    Debug.Log($"攻击速度: +{weaponData.AttackSpeed:P1}");
                }
            }
        }

        [ContextMenu("显示所有附件")]
        public void ShowAllAttachments()
        {
            var allAttachments = m_attachmentSystem.GetAllAttachments();
            Debug.Log($"=== 共有 {allAttachments.Count} 个附件 ===");
            
            foreach (var kvp in allAttachments)
            {
                var provider = kvp.Value;
                Debug.Log($"- {provider.ProviderId} (激活: {provider.IsActive})");
            }
        }

        [ContextMenu("显示所有槽位状态")]
        public void ShowAllSlotStatus()
        {
            var slots = m_attachmentSystem.GetAvailableSlots();
            Debug.Log("=== 槽位状态 ===");
            
            foreach (var slot in slots)
            {
                var isEmpty = slot.AttachedProviders.Count == 0;
                var status = isEmpty ? "空" : $"{slot.AttachedProviders.Count}个附件";
                Debug.Log($"{slot.SlotName} ({slot.SlotType}): {status}");
                
                if (!isEmpty)
                {
                    foreach (var provider in slot.AttachedProviders)
                    {
                        Debug.Log($"  - {provider.ProviderId}");
                    }
                }
            }
        }

        [ContextMenu("测试自由Buff槽位")]
        public void TestFreeBuff槽位()
        {
            Debug.Log("=== 测试自由Buff槽位系统 ===");
            
            // 添加多个不同的Buff
            for (int i = 1; i <= 5; i++)
            {
                var buff = BuffAttributeProvider.CreateDamageBuff($"test_buff_{i}", $"测试Buff{i}", i * 5f, 10f + i);
                bool success = m_buffManager.AddBuff(buff, "test_system");
                Debug.Log($"添加Buff{i}: {(success ? "成功" : "失败")}");
            }
            
            // 显示当前BuffContainer槽位状态
            var buffContainerAttachments = GetAttachmentsInSlot(AttachmentSlotType.BuffContainer);
            Debug.Log($"BuffContainer槽位现有{buffContainerAttachments.Count}个BuffAttributeProvider");
            
            // 显示所有Buff统计
            ShowAllBuffs();
        }

        [ContextMenu("测试组合属性")]
        public void TestComboAttributes()
        {
            Debug.Log("=== 当前属性状态 ===");
            Debug.Log($"力量: {m_attributeManager.GetAttributeValue(AttributeType.Strength):F1}");
            Debug.Log($"敏捷: {m_attributeManager.GetAttributeValue(AttributeType.Agility):F1}");
            Debug.Log($"智力: {m_attributeManager.GetAttributeValue(AttributeType.Intelligence):F1}");
            Debug.Log($"体质: {m_attributeManager.GetAttributeValue(AttributeType.Vitality):F1}");
            Debug.Log($"防御: {m_attributeManager.GetAttributeValue(AttributeType.Defense):F1}");
            Debug.Log("--- 衍生属性 ---");
            Debug.Log($"攻击力: {m_attributeManager.GetAttributeValue(AttributeType.Attack):F1}");
            Debug.Log($"最大生命值: {m_attributeManager.GetAttributeValue(AttributeType.MaxHealth):F1}");
            Debug.Log($"最大法力值: {m_attributeManager.GetAttributeValue(AttributeType.MaxMana):F1}");
            Debug.Log($"攻击速度: {m_attributeManager.GetAttributeValue(AttributeType.AttackSpeed):F1}");
            
            if (m_comboCalculator != null)
            {
                Debug.Log("--- 组合计算贡献 ---");
                Debug.Log($"组合攻击力: {m_comboCalculator.GetComboValue(AttributeType.Attack):F1}");
                Debug.Log($"组合生命值: {m_comboCalculator.GetComboValue(AttributeType.MaxHealth):F1}");
                Debug.Log($"组合法力值: {m_comboCalculator.GetComboValue(AttributeType.MaxMana):F1}");
                Debug.Log($"组合攻击速度: {m_comboCalculator.GetComboValue(AttributeType.AttackSpeed):F1}");
            }
        }

        [ContextMenu("增加智力")]
        public void TestAddIntelligence()
        {
            AddAttributePoint(AttributeType.Intelligence, 3f);
            Debug.Log("智力增加3点，查看魔法攻击变化");
        }

        [ContextMenu("增加防御")]
        public void TestAddDefense()
        {
            var currentDefense = m_attributeManager.GetAttributeValue(AttributeType.Defense);
            m_attributeManager.SetBaseAttribute(AttributeType.Defense, currentDefense + 1f);
            Debug.Log("防御增加1点，查看物理攻击变化");
        }

        [ContextMenu("显示组合规则")]
        public void ShowComboRules()
        {
            if (m_comboCalculator == null)
            {
                Debug.Log("组合计算器未初始化");
                return;
            }

            var rules = m_comboCalculator.GetAllRules();
            Debug.Log($"=== 共有{rules.Count}条组合规则 ===");
            
            foreach (var rule in rules)
            {
                var inputDesc = "";
                foreach (var input in rule.Inputs)
                {
                    if (inputDesc.Length > 0) inputDesc += " + ";
                    inputDesc += $"{input.Ratio}{input.Type.GetDisplayName()}";
                }
                
                Debug.Log($"{rule.Name}: {inputDesc} = {rule.Multiplier}x{rule.TargetType.GetDisplayName()} ({rule.Operation})");
            }
        }

        // === Buff 相关测试方法 ===

        [ContextMenu("添加攻击力Buff")]
        public void TestAddDamageBuff()
        {
            var damageBuff = BuffAttributeProvider.CreateDamageBuff("damage_boost", "攻击力提升", 10f, 15f);
            m_buffManager.AddBuff(damageBuff, "test");
        }

        [ContextMenu("添加可叠加防御Buff")]
        public void TestAddStackableDefenseBuff()
        {
            var defenseBuff = BuffAttributeProvider.CreateDefenseBuff("defense_stack", "防御力叠加", 5f, 20f);
            m_buffManager.AddBuff(defenseBuff, "test");
        }

        [ContextMenu("添加速度Buff")]
        public void TestAddSpeedBuff()
        {
            var speedBuff = BuffAttributeProvider.CreateSpeedBuff("speed_boost", "速度提升", 0.5f, 10f);
            m_buffManager.AddBuff(speedBuff, "test");
        }

        [ContextMenu("添加生命值Buff")]
        public void TestAddHealthBuff()
        {
            var healthBuff = BuffAttributeProvider.CreateHealthBuff("health_boost", "生命值提升", 50f, 30f, BuffStackType.Stack);
            m_buffManager.AddBuff(healthBuff, "test");
        }

        [ContextMenu("添加永久智力Buff")]
        public void TestAddPermanentIntBuff()
        {
            var intBuff = new BuffData("permanent_int", "永久智力加成", BuffStackType.Stack, 0f, BuffPriority.High);
            intBuff.Modifiers.Add(new AttributeModifier(AttributeType.Intelligence, 0f, 2f, 0f));
            m_buffManager.AddBuff(intBuff, "permanent");
        }

        [ContextMenu("移除攻击力Buff")]
        public void TestRemoveDamageBuff()
        {
            m_buffManager.RemoveBuff("damage_boost");
        }

        [ContextMenu("移除所有测试Buff")]
        public void TestRemoveAllTestBuffs()
        {
            m_buffManager.RemoveBuffsBySource("test");
        }

        [ContextMenu("显示所有Buff")]
        public void ShowAllBuffs()
        {
            var buffs = m_buffManager.GetAllBuffs();
            Debug.Log($"=== 当前有 {buffs.Count} 个Buff ===");
            
            foreach (var buff in buffs)
            {
                var timeInfo = buff.BuffData.IsPermanent ? "永久" : $"{buff.RemainingTime:F1}s剩余";
                var stackInfo = buff.CurrentStacks > 1 ? $" x{buff.CurrentStacks}" : "";
                Debug.Log($"- {buff.BuffData.BuffName}{stackInfo} ({timeInfo}) 来源:{buff.SourceId}");
            }
        }

        [ContextMenu("显示即将到期的Buff")]
        public void ShowExpiringBuffs()
        {
            var expiringBuffs = m_buffManager.GetExpiringBuffs(5f);
            Debug.Log($"=== {expiringBuffs.Count} 个Buff即将在5秒内到期 ===");
            
            foreach (var buff in expiringBuffs)
            {
                Debug.Log($"- {buff.BuffData.BuffName}: {buff.RemainingTime:F1}s剩余");
            }
        }

        [ContextMenu("暂停所有Buff")]
        public void TestPauseBuffs()
        {
            m_buffManager.PauseAllBuffs();
            Debug.Log("所有Buff已暂停");
        }

        [ContextMenu("恢复所有Buff")]
        public void TestResumeBuffs()
        {
            m_buffManager.ResumeAllBuffs();
            Debug.Log("所有Buff已恢复");
        }

        [ContextMenu("Buff统计信息")]
        public void ShowBuffStatistics()
        {
            m_buffManager.GetBuffStatistics(out int total, out int stackable, out int permanent);
            Debug.Log($"=== Buff统计 ===");
            Debug.Log($"总数: {total}");
            Debug.Log($"可叠加: {stackable}");
            Debug.Log($"永久: {permanent}");
        }
    }
}