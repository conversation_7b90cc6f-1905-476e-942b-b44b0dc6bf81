using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using XFrame;
using Logger = XFrame.Logger;

namespace Game.AceFall
{
    public class DynamicAttributeManager : MonoBehaviour
    {
        private readonly Dictionary<DynamicAttributeId, DynamicAttributeValue> m_baseAttributes = new();
        private readonly Dictionary<DynamicAttributeId, float> m_currentValues = new();
        private readonly Dictionary<string, IDynamicAttributeProvider> m_providers = new();
        private readonly Dictionary<DynamicAttributeId, List<DynamicAttributeModifier>> m_modifiersByAttribute = new();

        public event Action<DynamicAttributeId, float, float> OnAttributeChanged;
        public event Action<DynamicAttributeId, float, float> OnCurrentValueChanged;

        private void Awake()
        {
            Logger.Info("DynamicAttributeManager 初始化");
            InitializeDefaultAttributes();
        }

        private void InitializeDefaultAttributes()
        {
            var health = DynamicAttributeId.Register("Health");
            var maxHealth = DynamicAttributeId.Register("MaxHealth");
            var mana = DynamicAttributeId.Register("Mana");
            var maxMana = DynamicAttributeId.Register("MaxMana");
            var attack = DynamicAttributeId.Register("Attack");
            var defense = DynamicAttributeId.Register("Defense");
            var attackSpeed = DynamicAttributeId.Register("AttackSpeed");

            DynamicAttributeInfo.RegisterInfo(new DynamicAttributeInfo(health, "生命值")
                .SetHasCurrentValue(true, maxHealth)
                .SetCategory("生存"));
            DynamicAttributeInfo.RegisterInfo(new DynamicAttributeInfo(maxHealth, "最大生命值")
                .SetCategory("生存"));
            DynamicAttributeInfo.RegisterInfo(new DynamicAttributeInfo(mana, "法力值")
                .SetHasCurrentValue(true, maxMana)
                .SetCategory("魔法"));
            DynamicAttributeInfo.RegisterInfo(new DynamicAttributeInfo(maxMana, "最大法力值")
                .SetCategory("魔法"));
            DynamicAttributeInfo.RegisterInfo(new DynamicAttributeInfo(attack, "攻击力")
                .SetCategory("战斗"));
            DynamicAttributeInfo.RegisterInfo(new DynamicAttributeInfo(defense, "防御力")
                .SetCategory("战斗"));
            DynamicAttributeInfo.RegisterInfo(new DynamicAttributeInfo(attackSpeed, "攻击速度")
                .SetCategory("战斗"));

            SetBaseAttribute(maxHealth, 100f);
            SetBaseAttribute(health, 100f);
            SetBaseAttribute(maxMana, 50f);
            SetBaseAttribute(mana, 50f);
            SetBaseAttribute(attack, 10f);
            SetBaseAttribute(defense, 5f);
            SetBaseAttribute(attackSpeed, 1f);
        }

        public DynamicAttributeId RegisterAttribute(string name, string displayName = null, string category = "Custom")
        {
            var id = DynamicAttributeId.Register(name);
            var info = DynamicAttributeInfo.GetInfo(id);
            if (displayName != null)
                info.SetDisplayName(displayName);
            info.SetCategory(category);
            
            Logger.Info($"注册新属性: {name} ({displayName ?? name})");
            return id;
        }

        public void SetBaseAttribute(DynamicAttributeId attributeId, float value)
        {
            if (attributeId == DynamicAttributeId.Invalid)
                return;

            var oldValue = GetAttributeValue(attributeId);
            m_baseAttributes[attributeId] = new DynamicAttributeValue(value);

            var info = DynamicAttributeInfo.GetInfo(attributeId);
            if (info.HasCurrentValue)
            {
                if (!m_currentValues.ContainsKey(attributeId))
                {
                    m_currentValues[attributeId] = value;
                }
                else
                {
                    var currentValue = m_currentValues[attributeId];
                    var maxAttribute = info.MaxValueAttribute;
                    if (maxAttribute != DynamicAttributeId.Invalid)
                    {
                        var maxValue = GetAttributeValue(maxAttribute);
                        if (currentValue > maxValue)
                        {
                            SetCurrentValue(attributeId, maxValue, "MaxValueChanged");
                        }
                    }
                }
            }

            RecalculateAttribute(attributeId);
            var newValue = GetAttributeValue(attributeId);

            if (Math.Abs(oldValue - newValue) > 0.001f)
            {
                NotifyAttributeChanged(attributeId, oldValue, newValue, "BaseValue");
            }
        }

        public float GetAttributeValue(DynamicAttributeId attributeId)
        {
            if (attributeId == DynamicAttributeId.Invalid || !m_baseAttributes.TryGetValue(attributeId, out var baseValue))
                return 0f;

            var totalValue = baseValue;

            if (m_modifiersByAttribute.TryGetValue(attributeId, out var modifiers))
            {
                var sortedModifiers = modifiers.OrderBy(m => m.Priority);
                foreach (var modifier in sortedModifiers)
                {
                    totalValue += modifier.ToAttributeValue();
                }
            }

            var info = DynamicAttributeInfo.GetInfo(attributeId);
            return Mathf.Clamp(totalValue.FinalValue, info.MinValue, info.MaxValue);
        }

        public float GetCurrentValue(DynamicAttributeId attributeId)
        {
            if (attributeId == DynamicAttributeId.Invalid)
                return 0f;

            var info = DynamicAttributeInfo.GetInfo(attributeId);
            if (!info.HasCurrentValue)
                return GetAttributeValue(attributeId);

            return m_currentValues.TryGetValue(attributeId, out var value) ? value : 0f;
        }

        public void SetCurrentValue(DynamicAttributeId attributeId, float value, string reason = "")
        {
            if (attributeId == DynamicAttributeId.Invalid)
                return;

            var info = DynamicAttributeInfo.GetInfo(attributeId);
            if (!info.HasCurrentValue)
                return;

            var maxValue = info.MaxValueAttribute != DynamicAttributeId.Invalid 
                ? GetAttributeValue(info.MaxValueAttribute) 
                : info.MaxValue;
            var clampedValue = Mathf.Clamp(value, info.MinValue, maxValue);

            var oldValue = GetCurrentValue(attributeId);
            if (Math.Abs(oldValue - clampedValue) < 0.001f)
                return;

            m_currentValues[attributeId] = clampedValue;
            NotifyCurrentValueChanged(attributeId, oldValue, clampedValue, reason);
        }

        public void ModifyCurrentValue(DynamicAttributeId attributeId, float delta, string reason = "")
        {
            if (attributeId == DynamicAttributeId.Invalid)
                return;

            var currentValue = GetCurrentValue(attributeId);
            SetCurrentValue(attributeId, currentValue + delta, reason);
        }

        public void RegisterProvider(IDynamicAttributeProvider provider)
        {
            if (provider == null || m_providers.ContainsKey(provider.ProviderId))
                return;

            m_providers[provider.ProviderId] = provider;
            provider.OnModifiersChanged += OnProviderModifiersChanged;

            UpdateProviderModifiers(provider);
            Logger.Info($"注册属性提供者: {provider.ProviderId}");
        }

        public void UnregisterProvider(IDynamicAttributeProvider provider)
        {
            if (provider == null || !m_providers.ContainsKey(provider.ProviderId))
                return;

            RemoveProviderModifiers(provider);
            provider.OnModifiersChanged -= OnProviderModifiersChanged;
            m_providers.Remove(provider.ProviderId);
            Logger.Info($"注销属性提供者: {provider.ProviderId}");
        }

        private void OnProviderModifiersChanged(IDynamicAttributeProvider provider)
        {
            if (!provider.IsActive)
            {
                RemoveProviderModifiers(provider);
                return;
            }

            RemoveProviderModifiers(provider);
            UpdateProviderModifiers(provider);
        }

        private void UpdateProviderModifiers(IDynamicAttributeProvider provider)
        {
            if (!provider.IsActive)
                return;

            var modifiers = provider.GetAttributeModifiers();
            var affectedAttributes = new HashSet<DynamicAttributeId>();

            foreach (var modifier in modifiers)
            {
                if (modifier.AttributeId == DynamicAttributeId.Invalid)
                    continue;

                if (!m_modifiersByAttribute.ContainsKey(modifier.AttributeId))
                    m_modifiersByAttribute[modifier.AttributeId] = new List<DynamicAttributeModifier>();

                m_modifiersByAttribute[modifier.AttributeId].Add(modifier);
                affectedAttributes.Add(modifier.AttributeId);
            }

            foreach (var attributeId in affectedAttributes)
            {
                RecalculateAttribute(attributeId);
            }
        }

        private void RemoveProviderModifiers(IDynamicAttributeProvider provider)
        {
            var affectedAttributes = new HashSet<DynamicAttributeId>();

            foreach (var kvp in m_modifiersByAttribute.ToList())
            {
                var attributeId = kvp.Key;
                var modifiers = kvp.Value;

                for (int i = modifiers.Count - 1; i >= 0; i--)
                {
                    if (modifiers[i].Source == provider.ProviderId)
                    {
                        modifiers.RemoveAt(i);
                        affectedAttributes.Add(attributeId);
                    }
                }

                if (modifiers.Count == 0)
                    m_modifiersByAttribute.Remove(attributeId);
            }

            foreach (var attributeId in affectedAttributes)
            {
                RecalculateAttribute(attributeId);
            }
        }

        private void RecalculateAttribute(DynamicAttributeId attributeId)
        {
            var oldValue = GetAttributeValue(attributeId);
            var info = DynamicAttributeInfo.GetInfo(attributeId);

            if (info.HasCurrentValue && info.MaxValueAttribute != DynamicAttributeId.Invalid)
            {
                var currentValue = GetCurrentValue(attributeId);
                var newMaxValue = GetAttributeValue(info.MaxValueAttribute);

                if (currentValue > newMaxValue)
                {
                    SetCurrentValue(attributeId, newMaxValue, "MaxValueReduced");
                }
            }

            var newValue = GetAttributeValue(attributeId);
            if (Math.Abs(oldValue - newValue) > 0.001f)
            {
                NotifyAttributeChanged(attributeId, oldValue, newValue, "Recalculated");
            }
        }

        private void NotifyAttributeChanged(DynamicAttributeId attributeId, float oldValue, float newValue, string source)
        {
            OnAttributeChanged?.Invoke(attributeId, oldValue, newValue);
            Logger.Info($"属性变化: {attributeId} {oldValue:F2} -> {newValue:F2} ({source})");
        }

        private void NotifyCurrentValueChanged(DynamicAttributeId attributeId, float oldValue, float newValue, string reason)
        {
            OnCurrentValueChanged?.Invoke(attributeId, oldValue, newValue);
            Logger.Info($"当前值变化: {attributeId} {oldValue:F2} -> {newValue:F2} ({reason})");
        }

        public IReadOnlyDictionary<string, IDynamicAttributeProvider> GetProviders()
        {
            return m_providers;
        }

        public IReadOnlyList<DynamicAttributeModifier> GetModifiers(DynamicAttributeId attributeId)
        {
            return m_modifiersByAttribute.TryGetValue(attributeId, out var modifiers) ? modifiers : new List<DynamicAttributeModifier>();
        }

        public float GetAttributePercentage(DynamicAttributeId currentAttributeId)
        {
            var info = DynamicAttributeInfo.GetInfo(currentAttributeId);
            if (!info.HasCurrentValue || info.MaxValueAttribute == DynamicAttributeId.Invalid)
                return 1f;

            var currentValue = GetCurrentValue(currentAttributeId);
            var maxValue = GetAttributeValue(info.MaxValueAttribute);

            return maxValue > 0 ? currentValue / maxValue : 0f;
        }

        public IEnumerable<DynamicAttributeId> GetAllAttributes()
        {
            return DynamicAttributeId.GetAllRegistered();
        }

        public IEnumerable<DynamicAttributeId> GetAttributesByCategory(string category)
        {
            return DynamicAttributeId.GetAllRegistered()
                .Where(id => DynamicAttributeInfo.GetInfo(id).Category == category);
        }
    }
}